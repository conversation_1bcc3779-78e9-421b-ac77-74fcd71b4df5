import React, { createContext, useContext, useState, useEffect } from 'react';
import OnboardingModal from '../components/Onboarding/OnboardingModal';
import { useAuth } from './AuthContext';

interface OnboardingContextType {
  showOnboarding: () => void;
  hideOnboarding: () => void;
  isOnboardingComplete: boolean;
  setOnboardingComplete: (complete: boolean) => void;
}

const OnboardingContext = createContext<OnboardingContextType>({
  showOnboarding: () => {},
  hideOnboarding: () => {},
  isOnboardingComplete: false,
  setOnboardingComplete: () => {},
});

export const useOnboarding = () => useContext(OnboardingContext);

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false);
  const { isAuthenticated, isLoading } = useAuth();

  // Check if onboarding has been completed before and only show if authenticated
  useEffect(() => {
    // Don't show onboarding if still loading or not authenticated
    if (isLoading || !isAuthenticated) {
      return;
    }

    const onboardingComplete = localStorage.getItem('onboardingComplete');
    if (onboardingComplete === 'true') {
      setIsOnboardingComplete(true);
    } else {
      // If this is the first time and user is authenticated, show onboarding after a short delay
      const timer = setTimeout(() => {
        setIsOpen(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isLoading]);

  const showOnboarding = () => {
    setIsOpen(true);
  };

  const hideOnboarding = () => {
    setIsOpen(false);
  };

  const handleOnboardingComplete = (complete: boolean) => {
    setIsOnboardingComplete(complete);
    localStorage.setItem('onboardingComplete', complete.toString());
  };

  return (
    <OnboardingContext.Provider
      value={{
        showOnboarding,
        hideOnboarding,
        isOnboardingComplete,
        setOnboardingComplete: handleOnboardingComplete,
      }}
    >
      {children}
      <OnboardingModal
        isOpen={isOpen}
        onClose={() => {
          hideOnboarding();
          handleOnboardingComplete(true);
        }}
      />
    </OnboardingContext.Provider>
  );
};

export default OnboardingProvider;
