import React, { lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import ToastProvider from './contexts/ToastContext';
import OnboardingProvider from './contexts/OnboardingContext';
import ThemeProvider from './contexts/ThemeContext';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import SuspenseWrapper from './components/ui/SuspenseWrapper';

// Lazy load components to improve initial load time
const Dashboard = lazy(() => import('./pages/Dashboard'));
const InvoicesPage = lazy(() => import('./pages/InvoicesPage'));
const ClientsPage = lazy(() => import('./pages/ClientsPage'));
const PaymentsPage = lazy(() => import('./pages/PaymentsPage'));
const RecordPaymentPage = lazy(() => import('./pages/RecordPaymentPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
// RecurringPage is deprecated, using RecurringInvoicesPage instead
const RecurringInvoicesPage = lazy(() => import('./pages/RecurringInvoicesPage'));
const DocumentProcessingPage = lazy(() => import('./pages/DocumentProcessingPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const AboutPage = lazy(() => import('./pages/AboutPage'));
const DiagnosticsPage = lazy(() => import('./pages/DiagnosticsPage'));
const SignInPage = lazy(() => import('./pages/AuthPages/SignInPage'));
const SignUpPage = lazy(() => import('./pages/AuthPages/SignUpPage'));
const ForgotPasswordPage = lazy(() => import('./pages/AuthPages/ForgotPasswordPage'));

// Create a separate component for the app routes that can use the auth context
const AppRoutes: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-900"></div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Auth Routes */}
      <Route element={<AuthLayout />}>
        <Route path="/signin" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SignInPage />
          </Suspense>
        } />
        <Route path="/signup" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SignUpPage />
          </Suspense>
        } />
        <Route path="/forgot-password" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ForgotPasswordPage />
          </Suspense>
        } />
      </Route>

      {/* Public Routes */}
      <Route path="/about" element={
        <Suspense fallback={<div>Loading...</div>}>
          <AboutPage />
        </Suspense>
      } />
      <Route path="/diagnostics" element={
        <Suspense fallback={<div>Loading...</div>}>
          <DiagnosticsPage />
        </Suspense>
      } />

      {/* Protected Routes */}
      <Route path="/" element={
        isAuthenticated ? <MainLayout /> : <Navigate to="/signin" replace />
      }>
        {/* Redirect root to dashboard */}
        <Route index element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard as a secondary route */}
        <Route path="dashboard" element={
          <Suspense fallback={<div>Loading dashboard...</div>}>
            <Dashboard />
          </Suspense>
        } />

        {/* Invoice Routes */}
        <Route path="invoices" element={
          <Suspense fallback={<div>Loading invoices...</div>}>
            <InvoicesPage />
          </Suspense>
        } />
        <Route path="invoices/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoicesPage />
          </Suspense>
        } />
        <Route path="invoices/filter/:filter" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoicesPage />
          </Suspense>
        } />
        <Route path="invoices/:id/:action" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoicesPage />
          </Suspense>
        } />
        <Route path="invoices/:id" element={
          <Suspense fallback={<div>Loading...</div>}>
            <InvoicesPage />
          </Suspense>
        } />

        {/* Client Routes */}
        <Route path="clients" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientsPage />
          </Suspense>
            } />
        <Route path="clients/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientsPage />
          </Suspense>
        } />
        <Route path="clients/:id" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientsPage />
          </Suspense>
        } />
        <Route path="clients/:id/:action" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ClientsPage />
          </Suspense>
        } />

        {/* Payment Routes */}
        <Route path="payments" element={
          <Suspense fallback={<div>Loading...</div>}>
            <PaymentsPage />
          </Suspense>
        } />
        <Route path="payments/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <RecordPaymentPage />
          </Suspense>
        } />
        <Route path="reports" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ReportsPage />
          </Suspense>
        } />
        {/* Redirect /recurring to /recurring-invoices for backward compatibility */}
        <Route path="recurring" element={<Navigate to="/recurring-invoices" replace />} />

        {/* Recurring Invoices Routes */}
        <Route path="recurring-invoices" element={
          <Suspense fallback={<div>Loading...</div>}>
            <RecurringInvoicesPage />
              </Suspense>
        } />
        <Route path="recurring-invoices/new" element={
          <Suspense fallback={<div>Loading...</div>}>
            <RecurringInvoicesPage />
          </Suspense>
        } />
        <Route path="recurring-invoices/:id" element={
          <Suspense fallback={<div>Loading...</div>}>
            <RecurringInvoicesPage />
          </Suspense>
        } />
        <Route path="recurring-invoices/:id/:action" element={
          <Suspense fallback={<div>Loading...</div>}>
            <RecurringInvoicesPage />
          </Suspense>
        } />
        <Route path="settings" element={
          <Suspense fallback={<div>Loading...</div>}>
            <SettingsPage />
          </Suspense>
        } />
        <Route path="profile" element={
          <Suspense fallback={<div>Loading...</div>}>
            <ProfilePage />
          </Suspense>
        } />
        <Route path="documents" element={
          <Suspense fallback={<div>Loading...</div>}>
            <DocumentProcessingPage />
          </Suspense>
        } />
      </Route>

      {/* Redirect any unknown routes to dashboard or signin */}
      <Route path="*" element={<Navigate to={isAuthenticated ? "/" : "/signin"} replace />} />
    </Routes>
  );
};

function App() {
  return (
    <Router>
      <ThemeProvider>
        <ToastProvider>
          <AuthProvider>
            <OnboardingProvider>
              <AppRoutes />
            </OnboardingProvider>
          </AuthProvider>
        </ToastProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;